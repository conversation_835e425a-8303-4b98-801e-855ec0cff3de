#!/bin/bash
# -*- coding: utf-8 -*-
"""
风控AI问答系统服务启动脚本
支持API服务和Web界面的启动管理
"""

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查Python环境
check_python() {
    log_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装，请先安装Python3"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2)
    log_info "Python版本: $python_version"
    
    # 检查虚拟环境
    if [[ "$VIRTUAL_ENV" != "" ]]; then
        log_info "当前虚拟环境: $VIRTUAL_ENV"
    else
        log_warn "未检测到虚拟环境，建议使用虚拟环境"
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查Python依赖..."
    
    required_packages=(
        "chromadb"
        "sentence-transformers"
        "jieba"
        "zhconv"
        "langchain"
        "fastapi"
        "uvicorn"
        "streamlit"
        "requests"
    )
    
    missing_packages=()
    
    for package in "${required_packages[@]}"; do
        if ! python3 -c "import $package" &> /dev/null; then
            missing_packages+=("$package")
        fi
    done
    
    if [ ${#missing_packages[@]} -ne 0 ]; then
        log_error "缺少以下依赖包: ${missing_packages[*]}"
        log_info "正在安装缺少的依赖..."
        pip install "${missing_packages[@]}"
    else
        log_info "所有依赖已安装"
    fi
}

# 检查向量数据库
check_vector_db() {
    log_info "检查向量数据库..."
    
    if [ ! -d "data/vector_db" ]; then
        log_warn "向量数据库不存在，正在创建..."
        mkdir -p data/vector_db
    fi
    
    # 检查是否有数据
    if [ ! -f "data/vector_db/chroma.sqlite3" ]; then
        log_warn "向量数据库为空，建议运行文档导入脚本"
        log_info "运行命令: python scripts/import_documents.py --reset"
    else
        log_info "向量数据库已存在"
    fi
}

# 检查Ollama服务（可选）
check_ollama() {
    log_info "检查Ollama服务..."
    
    if command -v ollama &> /dev/null; then
        if curl -s http://localhost:11434/api/tags &> /dev/null; then
            log_info "Ollama服务运行正常"
            
            # 检查模型
            if ollama list | grep -q "qwen2.5:7b"; then
                log_info "Qwen2.5:7b模型已安装"
            else
                log_warn "Qwen2.5:7b模型未安装，将使用模拟LLM"
                log_info "安装命令: ollama pull qwen2.5:7b"
            fi
        else
            log_warn "Ollama服务未运行，将使用模拟LLM"
            log_info "启动命令: ollama serve"
        fi
    else
        log_warn "Ollama未安装，将使用模拟LLM"
        log_info "安装指南: https://ollama.ai/"
    fi
}

# 启动API服务
start_api() {
    log_info "启动API服务..."
    
    export PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH"
    
    cd "$PROJECT_ROOT"
    
    # 检查端口是否被占用
    if lsof -Pi :8000 -sTCP:LISTEN -t >/dev/null; then
        log_warn "端口8000已被占用，正在尝试停止现有服务..."
        pkill -f "uvicorn.*main:app" || true
        sleep 2
    fi
    
    log_info "在端口8000启动FastAPI服务..."
    nohup python3 -m uvicorn src.api.main:app --host 0.0.0.0 --port 8000 --reload > logs/api.log 2>&1 &
    
    API_PID=$!
    echo $API_PID > logs/api.pid
    
    # 等待服务启动
    sleep 3
    
    if curl -s http://localhost:8000/health &> /dev/null; then
        log_info "API服务启动成功 (PID: $API_PID)"
        log_info "API文档: http://localhost:8000/docs"
    else
        log_error "API服务启动失败"
        return 1
    fi
}

# 启动Web界面
start_web() {
    log_info "启动Web界面..."
    
    export PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH"
    
    cd "$PROJECT_ROOT"
    
    # 检查端口是否被占用
    if lsof -Pi :8501 -sTCP:LISTEN -t >/dev/null; then
        log_warn "端口8501已被占用，正在尝试停止现有服务..."
        pkill -f "streamlit.*run" || true
        sleep 2
    fi
    
    log_info "在端口8501启动Streamlit服务..."
    nohup streamlit run src/web/streamlit_app.py --server.port 8501 --server.address 0.0.0.0 > logs/web.log 2>&1 &
    
    WEB_PID=$!
    echo $WEB_PID > logs/web.pid
    
    # 等待服务启动
    sleep 5
    
    if curl -s http://localhost:8501 &> /dev/null; then
        log_info "Web界面启动成功 (PID: $WEB_PID)"
        log_info "Web界面: http://localhost:8501"
    else
        log_error "Web界面启动失败"
        return 1
    fi
}

# 停止服务
stop_services() {
    log_info "停止所有服务..."
    
    # 停止API服务
    if [ -f "logs/api.pid" ]; then
        API_PID=$(cat logs/api.pid)
        if kill -0 $API_PID 2>/dev/null; then
            log_info "停止API服务 (PID: $API_PID)"
            kill $API_PID
        fi
        rm -f logs/api.pid
    fi
    
    # 停止Web服务
    if [ -f "logs/web.pid" ]; then
        WEB_PID=$(cat logs/web.pid)
        if kill -0 $WEB_PID 2>/dev/null; then
            log_info "停止Web服务 (PID: $WEB_PID)"
            kill $WEB_PID
        fi
        rm -f logs/web.pid
    fi
    
    # 强制停止相关进程
    pkill -f "uvicorn.*main:app" || true
    pkill -f "streamlit.*run" || true
    
    log_info "所有服务已停止"
}

# 查看服务状态
status_services() {
    log_info "检查服务状态..."
    
    # API服务状态
    if curl -s http://localhost:8000/health &> /dev/null; then
        log_info "✅ API服务运行正常 (http://localhost:8000)"
    else
        log_warn "❌ API服务未运行"
    fi
    
    # Web服务状态
    if curl -s http://localhost:8501 &> /dev/null; then
        log_info "✅ Web界面运行正常 (http://localhost:8501)"
    else
        log_warn "❌ Web界面未运行"
    fi
    
    # 进程状态
    echo ""
    log_info "相关进程:"
    ps aux | grep -E "(uvicorn|streamlit)" | grep -v grep || log_info "无相关进程运行"
}

# 初始化环境
init_environment() {
    log_info "初始化环境..."
    
    # 创建必要目录
    mkdir -p logs data/vector_db
    
    # 检查环境
    check_python
    check_dependencies
    check_vector_db
    check_ollama
    
    log_info "环境初始化完成"
}

# 显示帮助信息
show_help() {
    echo "风控AI问答系统服务管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  init     - 初始化环境"
    echo "  start    - 启动所有服务"
    echo "  stop     - 停止所有服务"
    echo "  restart  - 重启所有服务"
    echo "  status   - 查看服务状态"
    echo "  api      - 仅启动API服务"
    echo "  web      - 仅启动Web界面"
    echo "  help     - 显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 init          # 初始化环境"
    echo "  $0 start         # 启动所有服务"
    echo "  $0 status        # 查看状态"
    echo "  $0 stop          # 停止服务"
}

# 主函数
main() {
    case "${1:-help}" in
        init)
            init_environment
            ;;
        start)
            init_environment
            start_api
            start_web
            echo ""
            log_info "🚀 所有服务启动完成!"
            log_info "📖 API文档: http://localhost:8000/docs"
            log_info "🌐 Web界面: http://localhost:8501"
            ;;
        stop)
            stop_services
            ;;
        restart)
            stop_services
            sleep 2
            init_environment
            start_api
            start_web
            ;;
        status)
            status_services
            ;;
        api)
            init_environment
            start_api
            ;;
        web)
            init_environment
            start_web
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
