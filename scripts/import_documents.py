#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档导入脚本
将法规文档和知识库文档导入向量数据库
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.vector_db.chroma_manager import ChromaManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DocumentImporter:
    """文档导入器"""
    
    def __init__(self):
        """初始化文档导入器"""
        self.chroma_manager = ChromaManager()
        self.regulations_dir = project_root / "data" / "regulations"
        self.knowledge_dir = project_root / "data" / "knowledge"
        
    def load_regulation_metadata(self, regulation_file: Path) -> Dict[str, Any]:
        """
        加载法规元数据

        Args:
            regulation_file: 法规文件路径

        Returns:
            元数据字典
        """
        # 构建元数据文件路径
        metadata_file = regulation_file.parent / f"{regulation_file.stem}_metadata.json"

        if metadata_file.exists():
            try:
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                return metadata
            except Exception as e:
                logger.warning(f"加载元数据失败 {metadata_file}: {e}")

        # 默认元数据
        return {
            "title": regulation_file.stem,
            "type": "regulation",
            "source": "unknown",
            "publish_date": "unknown",
            "effective_date": "unknown"
        }
    
    def import_regulations(self) -> bool:
        """
        导入法规文档
        
        Returns:
            是否成功导入
        """
        logger.info("开始导入法规文档...")
        
        success_count = 0
        total_count = 0
        
        # 遍历法规目录
        for regulation_file in self.regulations_dir.glob("*.txt"):
            total_count += 1
            
            try:
                # 读取文档内容
                with open(regulation_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 加载元数据
                metadata = self.load_regulation_metadata(regulation_file)
                metadata.update({
                    "category": "法律法规",
                    "file_path": str(regulation_file),
                    "file_name": regulation_file.name
                })
                
                # 生成文档ID
                document_id = f"regulation_{regulation_file.stem}"
                
                # 导入到向量数据库
                if self.chroma_manager.add_document(
                    document_id=document_id,
                    content=content,
                    metadata=metadata
                ):
                    success_count += 1
                    logger.info(f"成功导入法规: {regulation_file.name}")
                else:
                    logger.error(f"导入法规失败: {regulation_file.name}")
                    
            except Exception as e:
                logger.error(f"处理法规文件失败 {regulation_file}: {e}")
        
        logger.info(f"法规文档导入完成: {success_count}/{total_count}")
        return success_count == total_count
    
    def import_knowledge_documents(self) -> bool:
        """
        导入知识库文档
        
        Returns:
            是否成功导入
        """
        logger.info("开始导入知识库文档...")
        
        success_count = 0
        total_count = 0
        
        # 遍历知识库目录
        for knowledge_file in self.knowledge_dir.glob("*.md"):
            total_count += 1
            
            try:
                # 读取文档内容
                with open(knowledge_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 构建元数据
                metadata = {
                    "title": knowledge_file.stem,
                    "type": "knowledge",
                    "category": "风控知识",
                    "file_path": str(knowledge_file),
                    "file_name": knowledge_file.name,
                    "format": "markdown"
                }
                
                # 生成文档ID
                document_id = f"knowledge_{knowledge_file.stem}"
                
                # 导入到向量数据库
                if self.chroma_manager.add_document(
                    document_id=document_id,
                    content=content,
                    metadata=metadata
                ):
                    success_count += 1
                    logger.info(f"成功导入知识文档: {knowledge_file.name}")
                else:
                    logger.error(f"导入知识文档失败: {knowledge_file.name}")
                    
            except Exception as e:
                logger.error(f"处理知识文件失败 {knowledge_file}: {e}")
        
        # 处理JSON配置文件
        for json_file in self.knowledge_dir.glob("*.json"):
            total_count += 1
            
            try:
                # 读取JSON内容
                with open(json_file, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                
                # 将JSON转换为文本
                content = json.dumps(json_data, ensure_ascii=False, indent=2)
                
                # 构建元数据
                metadata = {
                    "title": json_file.stem,
                    "type": "configuration",
                    "category": "配置文件",
                    "file_path": str(json_file),
                    "file_name": json_file.name,
                    "format": "json"
                }
                
                # 生成文档ID
                document_id = f"config_{json_file.stem}"
                
                # 导入到向量数据库
                if self.chroma_manager.add_document(
                    document_id=document_id,
                    content=content,
                    metadata=metadata
                ):
                    success_count += 1
                    logger.info(f"成功导入配置文件: {json_file.name}")
                else:
                    logger.error(f"导入配置文件失败: {json_file.name}")
                    
            except Exception as e:
                logger.error(f"处理配置文件失败 {json_file}: {e}")
        
        logger.info(f"知识库文档导入完成: {success_count}/{total_count}")
        return success_count == total_count
    
    def import_risk_control_document(self) -> bool:
        """
        导入风控考虑要素文档
        
        Returns:
            是否成功导入
        """
        logger.info("开始导入风控考虑要素文档...")
        
        risk_control_file = project_root / "风控考虑要素.md"
        
        if not risk_control_file.exists():
            logger.warning(f"风控考虑要素文档不存在: {risk_control_file}")
            return False
        
        try:
            # 读取文档内容
            with open(risk_control_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 构建元数据
            metadata = {
                "title": "风控考虑要素",
                "type": "core_document",
                "category": "核心文档",
                "file_path": str(risk_control_file),
                "file_name": risk_control_file.name,
                "format": "markdown",
                "importance": "high"
            }
            
            # 生成文档ID
            document_id = "core_risk_control_elements"
            
            # 导入到向量数据库
            if self.chroma_manager.add_document(
                document_id=document_id,
                content=content,
                metadata=metadata,
                chunk_size=300  # 使用较小的块大小以提高检索精度
            ):
                logger.info("成功导入风控考虑要素文档")
                return True
            else:
                logger.error("导入风控考虑要素文档失败")
                return False
                
        except Exception as e:
            logger.error(f"处理风控考虑要素文档失败: {e}")
            return False
    
    def run_import(self, reset_db: bool = False) -> bool:
        """
        执行完整的文档导入流程
        
        Args:
            reset_db: 是否重置数据库
            
        Returns:
            是否成功导入
        """
        logger.info("开始文档导入流程...")
        
        # 重置数据库（如果需要）
        if reset_db:
            logger.info("重置向量数据库...")
            self.chroma_manager.reset_collection()
        
        # 导入各类文档
        results = []
        
        # 1. 导入核心风控文档
        results.append(self.import_risk_control_document())
        
        # 2. 导入法规文档
        results.append(self.import_regulations())
        
        # 3. 导入知识库文档
        results.append(self.import_knowledge_documents())
        
        # 获取最终统计信息
        stats = self.chroma_manager.get_collection_stats()
        logger.info(f"导入完成，数据库统计信息:")
        logger.info(f"  总文档块数: {stats.get('total_chunks', 0)}")
        logger.info(f"  总文档数: {stats.get('total_documents', 0)}")
        logger.info(f"  文档类型分布: {stats.get('document_types', {})}")
        
        success = all(results)
        if success:
            logger.info("所有文档导入成功！")
        else:
            logger.warning("部分文档导入失败，请检查日志")
        
        return success
    
    def test_search(self):
        """测试搜索功能"""
        logger.info("测试搜索功能...")
        
        test_queries = [
            "基金会管理条例",
            "慈善法",
            "声誉风险",
            "财务管理",
            "信息披露"
        ]
        
        for query in test_queries:
            logger.info(f"搜索查询: {query}")
            results = self.chroma_manager.search_documents(query, n_results=3)
            
            for i, result in enumerate(results, 1):
                logger.info(f"  结果{i}: {result['metadata'].get('title', 'Unknown')}")
                logger.info(f"    内容预览: {result['content'][:100]}...")
            
            logger.info("-" * 50)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="文档导入脚本")
    parser.add_argument("--reset", action="store_true", help="重置数据库")
    parser.add_argument("--test", action="store_true", help="测试搜索功能")
    
    args = parser.parse_args()
    
    # 创建导入器
    importer = DocumentImporter()
    
    # 执行导入
    success = importer.run_import(reset_db=args.reset)
    
    # 测试搜索（如果需要）
    if args.test:
        importer.test_search()
    
    if success:
        logger.info("文档导入流程完成！")
        sys.exit(0)
    else:
        logger.error("文档导入流程失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
