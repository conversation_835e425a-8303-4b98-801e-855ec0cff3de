#!/usr/bin/env python3
"""
法律法规文档下载脚本
自动下载和整理基金会相关的法律法规文档
"""

import os
import sys
import requests
import json
from pathlib import Path
from bs4 import BeautifulSoup
from typing import Dict, List
import time
import logging

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from config.settings import settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/download_regulations.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RegulationDownloader:
    """法律法规文档下载器"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.regulations_dir = settings.regulations_dir
        self.regulations_dir.mkdir(parents=True, exist_ok=True)
    
    def download_regulation(self, name: str, url: str) -> bool:
        """下载单个法规文档"""
        try:
            logger.info(f"开始下载: {name}")
            
            # 发送请求
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            # 解析HTML内容
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 提取正文内容
            content = self.extract_content(soup, name)
            
            if content:
                # 保存为文本文件
                file_path = self.regulations_dir / f"{name}.txt"
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                # 保存元数据
                metadata = {
                    'name': name,
                    'url': url,
                    'download_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'file_path': str(file_path),
                    'content_length': len(content)
                }
                
                metadata_path = self.regulations_dir / f"{name}_metadata.json"
                with open(metadata_path, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, ensure_ascii=False, indent=2)
                
                logger.info(f"成功下载: {name}, 内容长度: {len(content)}")
                return True
            else:
                logger.warning(f"未能提取到有效内容: {name}")
                return False
                
        except Exception as e:
            logger.error(f"下载失败 {name}: {str(e)}")
            return False
    
    def extract_content(self, soup: BeautifulSoup, name: str) -> str:
        """从HTML中提取法规正文内容"""
        content_selectors = [
            '.content',
            '.article-content',
            '.main-content',
            '#content',
            '.text-content',
            'article',
            '.law-content'
        ]
        
        # 尝试不同的选择器
        for selector in content_selectors:
            elements = soup.select(selector)
            if elements:
                content = elements[0].get_text(strip=True)
                if len(content) > 1000:  # 确保内容足够长
                    return self.clean_content(content)
        
        # 如果没有找到合适的选择器，尝试提取所有段落
        paragraphs = soup.find_all('p')
        if paragraphs:
            content = '\n'.join([p.get_text(strip=True) for p in paragraphs if p.get_text(strip=True)])
            if len(content) > 1000:
                return self.clean_content(content)
        
        # 最后尝试提取body内容
        body = soup.find('body')
        if body:
            content = body.get_text(strip=True)
            return self.clean_content(content)
        
        return ""
    
    def clean_content(self, content: str) -> str:
        """清理文档内容"""
        # 移除多余的空白字符
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        
        # 过滤掉导航、版权等无关内容
        filtered_lines = []
        skip_keywords = ['导航', '版权', '联系我们', '网站地图', '免责声明', '隐私政策']
        
        for line in lines:
            if not any(keyword in line for keyword in skip_keywords):
                if len(line) > 10:  # 过滤掉太短的行
                    filtered_lines.append(line)
        
        return '\n'.join(filtered_lines)
    
    def download_all_regulations(self) -> Dict[str, bool]:
        """下载所有法规文档"""
        results = {}
        
        for name, url in settings.regulation_urls.items():
            success = self.download_regulation(name, url)
            results[name] = success
            
            # 添加延迟避免被封IP
            time.sleep(2)
        
        return results

def create_manual_regulations():
    """创建手动整理的法规文档"""
    regulations_dir = settings.regulations_dir
    
    # 慈善法主要条文
    charity_law_content = """
中华人民共和国慈善法

第一章 总则

第一条 为了发展慈善事业，弘扬慈善文化，规范慈善活动，保护慈善组织、捐赠人、志愿者、受益人等慈善活动参与者的合法权益，促进社会进步，制定本法。

第二条 自然人、法人和其他组织开展慈善活动以及与慈善有关的活动，适用本法。其他法律有特别规定的，依照其规定。

第三条 本法所称慈善活动，是指自然人、法人和其他组织以捐赠财产或者提供服务等方式，自愿开展的下列公益活动：
（一）扶贫、济困；
（二）扶老、救孤、恤病、助残、优抚；
（三）救助自然灾害、事故灾难和公共卫生事件等突发事件造成的损害；
（四）促进教育、科学、文化、卫生、体育等事业的发展；
（五）防治污染和其他公害，保护和改善生态环境；
（六）符合本法规定的其他公益活动。

第四条 开展慈善活动，应当遵循合法、自愿、诚信、非营利的原则，不得违背社会公德，不得危害国家安全、损害社会公共利益和他人合法权益。

第五条 国家鼓励和支持自然人、法人和其他组织践行社会主义核心价值观，弘扬中华民族传统美德，依法开展慈善活动。

第六条 国务院民政部门主管全国慈善工作，县级以上地方各级人民政府民政部门主管本行政区域内的慈善工作。
县级以上人民政府有关部门依照本法和其他有关法律法规，在各自的职责范围内做好相关工作。

第二章 慈善组织

第八条 本法所称慈善组织，是指依法成立、符合本法规定，以面向社会开展慈善活动为宗旨的非营利性组织。
慈善组织可以采取基金会、社会团体、社会服务机构等组织形式。

第九条 慈善组织应当符合下列条件：
（一）以开展慈善活动为宗旨；
（二）不以营利为目的；
（三）有自己的名称和住所；
（四）有组织章程；
（五）有必要的财产；
（六）有符合条件的组织机构和负责人；
（七）法律、行政法规规定的其他条件。

第十条 设立慈善组织，应当向县级以上人民政府民政部门申请登记，民政部门应当自受理申请之日起三十日内作出决定。符合本法规定条件的，准予登记并向社会公告；不符合本法规定条件的，不予登记并书面说明理由。

第十一条 慈善组织的章程，应当符合法律法规的规定，并载明下列事项：
（一）名称和住所；
（二）组织形式；
（三）设立目的和活动范围；
（四）财产来源及构成；
（五）决策、执行机构的组成及职责；
（六）内部监督机制；
（七）财产管理使用制度；
（八）项目管理制度；
（九）终止情形及终止后财产的处理；
（十）其他重要事项。
"""
    
    # 保存慈善法内容
    with open(regulations_dir / "慈善法.txt", 'w', encoding='utf-8') as f:
        f.write(charity_law_content)
    
    # 基金会管理条例主要条文
    foundation_regulation_content = """
基金会管理条例

第一章 总则

第一条 为了规范基金会的组织和活动，维护基金会、捐赠人和受益人的合法权益，促进社会公益事业发展，制定本条例。

第二条 本条例所称基金会，是指利用自然人、法人或者其他组织捐赠的财产，以从事公益事业为目的，按照本条例的规定成立的非营利性法人。

第三条 基金会分为面向公众募捐的基金会（以下简称公募基金会）和不得面向公众募捐的基金会（以下简称非公募基金会）。公募基金会按照募捐的地域范围，分为全国性公募基金会和地方性公募基金会。

第四条 基金会必须遵守宪法、法律、法规、规章，坚持为公共利益服务。

第五条 基金会依照章程从事公益活动，应当遵循公开、透明的原则。

第六条 国务院民政部门和省、自治区、直辖市人民政府民政部门是基金会的登记管理机关。
国务院民政部门负责下列基金会、基金会代表机构的登记管理工作：
（一）全国性公募基金会；
（二）拟由非内地居民担任法定代表人的基金会；
（三）原始基金超过2000万元，发起人向国务院民政部门提出设立申请的非公募基金会；
（四）境外基金会在中国内地设立的代表机构。

第二章 设立、变更和注销

第七条 设立基金会，应当具备下列条件：
（一）为特定的公益目的而设立；
（二）全国性公募基金会的原始基金不低于800万元人民币，地方性公募基金会的原始基金不低于400万元人民币，非公募基金会的原始基金不低于200万元人民币；原始基金必须为到账货币资金；
（三）有规范的名称、章程、组织机构以及与其开展活动相适应的专职工作人员；
（四）有固定的住所；
（五）能够独立承担民事责任。

第八条 设立基金会，应当向登记管理机关提交下列文件：
（一）申请书；
（二）章程草案；
（三）验资证明和住所证明；
（四）理事名单、身份证明以及拟任理事长、副理事长、秘书长简历；
（五）业务主管单位同意设立的文件。

第三章 组织机构

第十五条 基金会设理事会，理事为5人至25人。理事任期由章程规定，但每届任期不得超过5年。理事任期届满，连选可以连任。

第十六条 理事会是基金会的决策机构，依法行使章程规定的职权。

第十七条 理事会每年至少召开2次会议。理事会会议须有2/3以上理事出席方能召开；理事会决议须经出席理事过半数通过方为有效。

第四章 财产的管理和使用

第二十七条 基金会的财产及其他收入受法律保护，任何单位和个人不得私分、挪用、截留。

第二十八条 基金会应当按照章程规定的宗旨和公益活动的业务范围使用财产；捐赠协议明确了具体使用方式的捐赠，根据捐赠协议的约定使用。

第二十九条 公募基金会每年用于从事章程规定的公益事业支出，不得低于上一年总收入的70%；非公募基金会每年用于从事章程规定的公益事业支出，不得低于上一年基金余额的8%。

第三十条 基金会工作人员工资福利和行政办公支出不得超过当年总支出的10%。

第三十一条 基金会可以将财产委托给受托人运营管理，但应当与受托人签订书面委托协议。
"""
    
    # 保存基金会管理条例内容
    with open(regulations_dir / "基金会管理条例.txt", 'w', encoding='utf-8') as f:
        f.write(foundation_regulation_content)
    
    logger.info("手动创建的法规文档已保存")

def main():
    """主函数"""
    logger.info("开始下载法律法规文档...")
    
    # 创建下载器
    downloader = RegulationDownloader()
    
    # 首先创建手动整理的核心法规
    create_manual_regulations()
    
    # 尝试自动下载其他法规
    results = downloader.download_all_regulations()
    
    # 输出结果
    logger.info("下载结果:")
    for name, success in results.items():
        status = "成功" if success else "失败"
        logger.info(f"  {name}: {status}")
    
    # 生成法规清单
    create_regulation_index()
    
    logger.info("法律法规文档收集完成!")

def create_regulation_index():
    """创建法规文档索引"""
    regulations_dir = settings.regulations_dir
    index_data = {
        "regulations": [],
        "last_updated": time.strftime('%Y-%m-%d %H:%M:%S')
    }
    
    # 扫描法规目录
    for file_path in regulations_dir.glob("*.txt"):
        if not file_path.name.endswith("_metadata.json"):
            regulation_info = {
                "name": file_path.stem,
                "file_path": str(file_path),
                "file_size": file_path.stat().st_size,
                "last_modified": time.strftime('%Y-%m-%d %H:%M:%S', 
                                             time.localtime(file_path.stat().st_mtime))
            }
            
            # 读取元数据（如果存在）
            metadata_path = regulations_dir / f"{file_path.stem}_metadata.json"
            if metadata_path.exists():
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                    regulation_info.update(metadata)
            
            index_data["regulations"].append(regulation_info)
    
    # 保存索引文件
    index_path = regulations_dir / "regulations_index.json"
    with open(index_path, 'w', encoding='utf-8') as f:
        json.dump(index_data, f, ensure_ascii=False, indent=2)
    
    logger.info(f"法规索引已创建: {index_path}")

if __name__ == "__main__":
    main()
