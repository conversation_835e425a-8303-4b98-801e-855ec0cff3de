#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI问答系统（不依赖Ollama）
"""

import sys
from pathlib import Path
import logging

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.vector_db.chroma_manager import ChromaManager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockLLM:
    """模拟LLM，用于测试"""
    
    def __init__(self):
        self._llm_type = "mock_llm"
    
    def __call__(self, prompt: str, **kwargs) -> str:
        """模拟LLM调用"""
        # 简单的基于关键词的回答
        prompt_lower = prompt.lower()
        
        if "财务管理" in prompt_lower or "财务" in prompt_lower:
            return """基于风控知识库，基金会财务管理的主要风险包括：

1. **资金安全风险**
   - 建立完善的财务内控制度
   - 实行资金审批和监督机制
   - 定期进行财务审计

2. **合规风险**
   - 严格按照《慈善法》和《基金会管理条例》要求
   - 确保资金使用符合公益目的
   - 及时进行信息披露

3. **透明度风险**
   - 建立公开透明的财务报告制度
   - 定期公布资金使用情况
   - 接受社会监督

建议建立完善的财务管理制度，确保资金安全和合规使用。"""

        elif "声誉风险" in prompt_lower or "声誉" in prompt_lower:
            return """声誉风险管控是基金会风险管理的重要组成部分：

1. **监测机制**
   - 建立社交媒体监测系统
   - 关注微博、微信、抖音等平台
   - 及时发现负面信息

2. **应对策略**
   - 制定危机公关预案
   - 建立快速响应机制
   - 保持透明沟通

3. **预防措施**
   - 加强项目管理
   - 提高运营透明度
   - 建立良好的公众关系

参考郭美美事件等案例，声誉风险一旦爆发影响巨大，预防胜于治疗。"""

        elif "慈善法" in prompt_lower or "信息披露" in prompt_lower:
            return """根据《慈善法》规定，基金会信息披露要求包括：

1. **年度报告披露**
   - 每年3月31日前公布上一年度工作报告
   - 包括财务状况、业务活动等信息

2. **重大事项披露**
   - 重大捐赠收入和支出
   - 重大资产变动
   - 重大人事变动

3. **财务信息披露**
   - 年度财务会计报告
   - 资金使用情况
   - 管理费用支出

4. **项目信息披露**
   - 公益项目实施情况
   - 项目资金使用效果
   - 受益人信息（在保护隐私前提下）

信息披露是建立公信力的基础，必须真实、完整、及时。"""

        else:
            return f"""基于您的问题，我建议您：

1. 查阅相关法律法规，特别是《慈善法》和《基金会管理条例》
2. 建立完善的内控制度和风险管理机制
3. 加强信息披露和透明度建设
4. 关注声誉风险管控

如需更详细的专业建议，建议咨询专业的法律和财务顾问。

（注：这是模拟回答，实际系统将基于完整的知识库提供更准确的建议）"""

class SimplifiedQASystem:
    """简化的问答系统"""
    
    def __init__(self, chroma_manager: ChromaManager):
        self.chroma_manager = chroma_manager
        self.llm = MockLLM()
        logger.info("简化问答系统初始化完成")
    
    def ask_question(self, question: str, search_k: int = 5) -> dict:
        """回答问题"""
        try:
            # 检索相关文档
            results = self.chroma_manager.search_documents(question, n_results=search_k)
            
            # 构建上下文
            context = "\n\n".join([
                f"文档{i+1}：{result['content'][:200]}..."
                for i, result in enumerate(results)
            ])
            
            # 构建提示
            prompt = f"""基于以下知识库内容回答问题：

相关内容：
{context}

用户问题：{question}

请提供专业的风控建议："""
            
            # 生成回答
            answer = self.llm(prompt)
            
            return {
                "question": question,
                "answer": answer,
                "relevant_documents": len(results),
                "sources": [
                    {
                        "content": result["content"][:200] + "...",
                        "metadata": result["metadata"]
                    }
                    for result in results
                ]
            }
            
        except Exception as e:
            logger.error(f"回答问题时出错: {e}")
            return {
                "question": question,
                "answer": "抱歉，处理您的问题时出现错误。",
                "error": str(e)
            }
    
    def get_system_stats(self) -> dict:
        """获取系统统计"""
        try:
            return self.chroma_manager.get_collection_stats()
        except Exception as e:
            logger.error(f"获取统计信息时出错: {e}")
            return {"error": str(e)}

def main():
    """测试主函数"""
    print("=== 风控AI问答系统测试 ===\n")
    
    try:
        # 初始化向量数据库
        print("正在初始化向量数据库...")
        chroma_manager = ChromaManager()
        
        # 检查数据库状态
        stats = chroma_manager.get_collection_stats()
        print(f"数据库状态：{stats}")
        
        if stats.get('total_documents', 0) == 0:
            print("⚠️  向量数据库为空，请先运行文档导入脚本")
            return
        
        # 初始化问答系统
        print("正在初始化问答系统...")
        qa_system = SimplifiedQASystem(chroma_manager)
        
        # 测试问题
        test_questions = [
            "基金会在财务管理方面需要注意哪些风险？",
            "如何建立有效的声誉风险管控机制？",
            "慈善法对基金会信息披露有什么要求？",
            "基金会如何防范网络安全风险？"
        ]
        
        print("\n=== 开始测试问答功能 ===\n")
        
        for i, question in enumerate(test_questions, 1):
            print(f"问题 {i}：{question}")
            print("-" * 60)
            
            result = qa_system.ask_question(question)
            
            if "error" not in result:
                print(f"回答：{result['answer']}")
                print(f"\n相关文档数：{result['relevant_documents']}")
                
                if result.get('sources'):
                    print("\n信息来源：")
                    for j, source in enumerate(result['sources'][:2], 1):  # 只显示前2个来源
                        print(f"  来源{j}：{source['metadata'].get('title', '未知文档')}")
                        print(f"  内容：{source['content']}")
            else:
                print(f"❌ 错误：{result['error']}")
            
            print("\n" + "=" * 80 + "\n")
        
        # 测试搜索功能
        print("=== 测试搜索功能 ===\n")
        search_query = "财务管理"
        print(f"搜索关键词：{search_query}")
        
        search_results = chroma_manager.search_documents(search_query, n_results=3)
        print(f"搜索结果数：{len(search_results)}")
        
        for i, result in enumerate(search_results, 1):
            print(f"\n结果 {i}：")
            print(f"  文档：{result['metadata'].get('title', '未知文档')}")
            print(f"  内容：{result['content'][:150]}...")
        
        print(f"\n✅ 测试完成！系统运行正常。")
        
    except Exception as e:
        print(f"❌ 测试失败：{e}")
        logger.error(f"测试过程中出错: {e}")

if __name__ == "__main__":
    main()
