version: '3.8'

services:
  # Ollama本地AI模型服务
  ollama:
    image: ollama/ollama:latest
    container_name: foundation-risk-ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    restart: unless-stopped
    networks:
      - foundation-risk-network

  # 基金会风控AI问答系统API服务
  api:
    build: .
    container_name: foundation-risk-api
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    environment:
      - OLLAMA_HOST=http://ollama:11434
      - API_HOST=0.0.0.0
      - API_PORT=8000
    depends_on:
      - ollama
    restart: unless-stopped
    networks:
      - foundation-risk-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Web界面服务
  web:
    build: .
    container_name: foundation-risk-web
    ports:
      - "8501:8501"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    environment:
      - API_HOST=api
      - API_PORT=8000
      - WEB_HOST=0.0.0.0
      - WEB_PORT=8501
    depends_on:
      - api
    restart: unless-stopped
    networks:
      - foundation-risk-network
    command: ["streamlit", "run", "src/web/app.py", "--server.address", "0.0.0.0", "--server.port", "8501"]

  # 向量数据库服务（可选，如果需要独立部署）
  chroma:
    image: chromadb/chroma:latest
    container_name: foundation-risk-chroma
    ports:
      - "8002:8000"
    volumes:
      - chroma_data:/chroma/chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
    restart: unless-stopped
    networks:
      - foundation-risk-network

  # 监控服务（Prometheus）
  prometheus:
    image: prom/prometheus:latest
    container_name: foundation-risk-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - foundation-risk-network

  # 可视化监控（Grafana）
  grafana:
    image: grafana/grafana:latest
    container_name: foundation-risk-grafana
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    restart: unless-stopped
    networks:
      - foundation-risk-network

volumes:
  ollama_data:
    driver: local
  chroma_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  foundation-risk-network:
    driver: bridge
