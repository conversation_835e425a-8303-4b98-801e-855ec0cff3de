#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
风控AI问答系统Streamlit Web界面
提供用户友好的交互界面
"""

import streamlit as st
import requests
import json
from datetime import datetime
from typing import Dict, Any, Optional
import pandas as pd

# 页面配置
st.set_page_config(
    page_title="风控AI问答系统",
    page_icon="🛡️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 配置
API_BASE_URL = "http://localhost:8000"

# 样式
st.markdown("""
<style>
.main-header {
    font-size: 2.5rem;
    color: #1f77b4;
    text-align: center;
    margin-bottom: 2rem;
}
.sub-header {
    font-size: 1.5rem;
    color: #ff7f0e;
    margin-bottom: 1rem;
}
.info-box {
    background-color: #f0f2f6;
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 1rem 0;
}
.success-box {
    background-color: #d4edda;
    color: #155724;
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 1rem 0;
}
.error-box {
    background-color: #f8d7da;
    color: #721c24;
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 1rem 0;
}
</style>
""", unsafe_allow_html=True)

def check_api_health() -> bool:
    """检查API服务状态"""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        return response.status_code == 200 and response.json().get("status") == "healthy"
    except:
        return False

def call_api(endpoint: str, method: str = "GET", data: Optional[Dict] = None) -> Dict[str, Any]:
    """调用API"""
    try:
        url = f"{API_BASE_URL}{endpoint}"
        
        if method == "GET":
            response = requests.get(url, timeout=30)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=30)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")
        
        if response.status_code == 200:
            return {"success": True, "data": response.json()}
        else:
            return {"success": False, "error": f"API错误: {response.status_code}"}
            
    except requests.exceptions.Timeout:
        return {"success": False, "error": "请求超时，请稍后重试"}
    except requests.exceptions.ConnectionError:
        return {"success": False, "error": "无法连接到API服务"}
    except Exception as e:
        return {"success": False, "error": f"请求失败: {str(e)}"}

def main():
    """主函数"""
    
    # 标题
    st.markdown('<h1 class="main-header">🛡️ 风控AI问答系统</h1>', unsafe_allow_html=True)
    st.markdown('<p style="text-align: center; color: #666;">基于AI的公益基金会风险控制智能问答系统</p>', unsafe_allow_html=True)
    
    # 侧边栏
    with st.sidebar:
        st.markdown("### 🔧 系统功能")
        
        # API状态检查
        if check_api_health():
            st.success("✅ API服务正常")
        else:
            st.error("❌ API服务不可用")
            st.stop()
        
        # 功能选择
        function = st.selectbox(
            "选择功能",
            ["💬 智能问答", "⚠️ 风险评估", "📋 法规查询", "🔍 文档搜索", "📊 系统统计"]
        )
        
        # 高级设置
        st.markdown("### ⚙️ 高级设置")
        search_k = st.slider("检索文档数量", 1, 20, 5)
        include_sources = st.checkbox("显示信息来源", True)
        
        # 清除记忆按钮
        if st.button("🗑️ 清除对话记忆"):
            result = call_api("/clear-memory", "POST")
            if result["success"]:
                st.success("对话记忆已清除")
            else:
                st.error(f"清除失败: {result['error']}")
    
    # 主内容区域
    if function == "💬 智能问答":
        show_qa_interface(search_k, include_sources)
    elif function == "⚠️ 风险评估":
        show_risk_assessment_interface(search_k)
    elif function == "📋 法规查询":
        show_regulation_interface()
    elif function == "🔍 文档搜索":
        show_search_interface(search_k)
    elif function == "📊 系统统计":
        show_stats_interface()

def show_qa_interface(search_k: int, include_sources: bool):
    """智能问答界面"""
    st.markdown('<h2 class="sub-header">💬 智能问答</h2>', unsafe_allow_html=True)
    
    # 预设问题
    st.markdown("#### 🎯 常见问题")
    preset_questions = [
        "基金会在财务管理方面需要注意哪些风险？",
        "如何建立有效的声誉风险管控机制？",
        "慈善法对基金会信息披露有什么要求？",
        "基金会如何防范网络安全风险？",
        "公益项目执行中的主要风险点有哪些？"
    ]
    
    cols = st.columns(2)
    for i, question in enumerate(preset_questions):
        with cols[i % 2]:
            if st.button(f"📝 {question}", key=f"preset_{i}"):
                st.session_state.question = question
    
    # 问题输入
    question = st.text_area(
        "请输入您的问题：",
        value=st.session_state.get("question", ""),
        height=100,
        placeholder="例如：基金会在财务管理方面需要注意哪些风险？"
    )
    
    if st.button("🚀 提交问题", type="primary"):
        if question.strip():
            with st.spinner("AI正在思考中..."):
                result = call_api("/ask", "POST", {
                    "question": question,
                    "search_k": search_k,
                    "include_sources": include_sources
                })
                
                if result["success"]:
                    data = result["data"]
                    
                    # 显示回答
                    st.markdown("#### 🤖 AI回答")
                    st.markdown(f'<div class="success-box">{data["answer"]}</div>', unsafe_allow_html=True)
                    
                    # 显示元信息
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("相关文档", data["relevant_documents"])
                    with col2:
                        st.metric("回答时间", data["timestamp"].split("T")[1][:8])
                    with col3:
                        if include_sources and data.get("sources"):
                            st.metric("信息来源", len(data["sources"]))
                    
                    # 显示信息来源
                    if include_sources and data.get("sources"):
                        st.markdown("#### 📚 信息来源")
                        for i, source in enumerate(data["sources"], 1):
                            with st.expander(f"来源 {i}: {source['metadata'].get('title', '未知文档')}"):
                                st.text(source["content"])
                                st.json(source["metadata"])
                else:
                    st.error(f"❌ {result['error']}")
        else:
            st.warning("请输入问题")

def show_risk_assessment_interface(search_k: int):
    """风险评估界面"""
    st.markdown('<h2 class="sub-header">⚠️ 风险评估</h2>', unsafe_allow_html=True)
    
    # 场景模板
    st.markdown("#### 📋 场景模板")
    templates = {
        "合作项目": "基金会计划与某企业合作开展大型公益项目，涉及资金1000万元，项目周期2年",
        "网络筹款": "基金会准备在网络平台开展大规模筹款活动，目标筹款500万元",
        "海外项目": "基金会计划在海外开展教育援助项目，涉及跨境资金转移",
        "志愿者管理": "基金会组织大型志愿者活动，预计参与人数超过1000人"
    }
    
    selected_template = st.selectbox("选择场景模板", ["自定义"] + list(templates.keys()))
    
    # 场景描述
    if selected_template != "自定义":
        default_scenario = templates[selected_template]
    else:
        default_scenario = ""
    
    scenario = st.text_area(
        "请描述风险场景：",
        value=default_scenario,
        height=150,
        placeholder="请详细描述需要评估的风险场景..."
    )
    
    if st.button("🔍 开始风险评估", type="primary"):
        if scenario.strip():
            with st.spinner("正在进行风险评估..."):
                result = call_api("/assess-risk", "POST", {
                    "scenario": scenario,
                    "search_k": search_k
                })
                
                if result["success"]:
                    data = result["data"]
                    
                    # 显示评估结果
                    st.markdown("#### 📊 风险评估报告")
                    st.markdown(f'<div class="info-box">{data["assessment"]}</div>', unsafe_allow_html=True)
                    
                    # 显示元信息
                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric("知识来源", data["knowledge_sources"])
                    with col2:
                        st.metric("评估时间", data["timestamp"].split("T")[1][:8])
                else:
                    st.error(f"❌ {result['error']}")
        else:
            st.warning("请输入风险场景描述")

def show_regulation_interface():
    """法规查询界面"""
    st.markdown('<h2 class="sub-header">📋 法规查询</h2>', unsafe_allow_html=True)
    
    # 法规列表
    regulations = [
        "慈善法", "基金会管理条例", "公益事业捐赠法",
        "个人信息保护法", "网络安全法", "数据安全法",
        "民间非营利组织会计制度"
    ]
    
    col1, col2 = st.columns([2, 1])
    with col1:
        regulation_name = st.text_input("请输入法规名称：", placeholder="例如：慈善法")
    with col2:
        selected_reg = st.selectbox("或选择法规", [""] + regulations)
        if selected_reg:
            regulation_name = selected_reg
    
    if st.button("🔍 查询法规", type="primary"):
        if regulation_name.strip():
            with st.spinner("正在查询法规信息..."):
                result = call_api("/regulation", "POST", {
                    "regulation_name": regulation_name
                })
                
                if result["success"]:
                    data = result["data"]
                    
                    if data["found"]:
                        st.markdown(f"#### 📖 {data['regulation']}")
                        st.markdown(f'<div class="info-box">{data["content"]}</div>', unsafe_allow_html=True)
                        
                        col1, col2 = st.columns(2)
                        with col1:
                            st.metric("文档片段", data["chunks"])
                        with col2:
                            st.metric("查询时间", data["timestamp"].split("T")[1][:8])
                    else:
                        st.warning(f"❌ {data['message']}")
                else:
                    st.error(f"❌ {result['error']}")
        else:
            st.warning("请输入法规名称")

def show_search_interface(search_k: int):
    """文档搜索界面"""
    st.markdown('<h2 class="sub-header">🔍 文档搜索</h2>', unsafe_allow_html=True)
    
    query = st.text_input(
        "请输入搜索关键词：",
        placeholder="例如：财务管理、信息披露、风险控制"
    )
    
    if st.button("🔍 搜索文档", type="primary"):
        if query.strip():
            with st.spinner("正在搜索文档..."):
                result = call_api(f"/search?query={query}&k={search_k}")
                
                if result["success"]:
                    data = result["data"]
                    
                    st.markdown(f"#### 📄 搜索结果 ({data['count']} 个)")
                    
                    for i, doc in enumerate(data["results"], 1):
                        with st.expander(f"文档 {i}: {doc['metadata'].get('title', '未知文档')}"):
                            st.text(doc["content"])
                            st.markdown("**元数据:**")
                            st.json(doc["metadata"])
                else:
                    st.error(f"❌ {result['error']}")
        else:
            st.warning("请输入搜索关键词")

def show_stats_interface():
    """系统统计界面"""
    st.markdown('<h2 class="sub-header">📊 系统统计</h2>', unsafe_allow_html=True)
    
    if st.button("🔄 刷新统计信息"):
        with st.spinner("正在获取系统统计信息..."):
            result = call_api("/stats")
            
            if result["success"]:
                data = result["data"]
                
                # 数据库统计
                st.markdown("#### 📚 数据库统计")
                db_stats = data["database"]
                
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("总文档数", db_stats.get("total_documents", 0))
                with col2:
                    st.metric("总文档块数", db_stats.get("total_chunks", 0))
                with col3:
                    st.metric("集合名称", db_stats.get("collection_name", "未知"))
                
                # 文档类型分布
                if "document_types" in db_stats:
                    st.markdown("#### 📈 文档类型分布")
                    doc_types = db_stats["document_types"]
                    df = pd.DataFrame(list(doc_types.items()), columns=["类型", "数量"])
                    st.bar_chart(df.set_index("类型"))
                
                # 系统信息
                st.markdown("#### ⚙️ 系统信息")
                col1, col2 = st.columns(2)
                with col1:
                    st.info(f"**LLM类型:** {data['llm_type']}")
                with col2:
                    st.info(f"**记忆功能:** {'启用' if data['memory_enabled'] else '禁用'}")
                
                st.success(f"**更新时间:** {data['timestamp']}")
                
            else:
                st.error(f"❌ {result['error']}")

if __name__ == "__main__":
    main()
