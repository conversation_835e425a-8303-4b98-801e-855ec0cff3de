#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
风控AI问答系统Web API
基于FastAPI的高性能API服务
"""

import os
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
import json
from datetime import datetime

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn

import sys
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.vector_db.chroma_manager import ChromaManager
from src.ai_agent.qa_system import RiskControlQASystem

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局变量
qa_system: Optional[RiskControlQASystem] = None

# Pydantic模型
class QuestionRequest(BaseModel):
    """问题请求模型"""
    question: str = Field(..., description="用户问题", min_length=1, max_length=1000)
    search_k: int = Field(5, description="检索文档数量", ge=1, le=20)
    include_sources: bool = Field(True, description="是否包含信息来源")

class RiskAssessmentRequest(BaseModel):
    """风险评估请求模型"""
    scenario: str = Field(..., description="风险场景描述", min_length=1, max_length=2000)
    search_k: int = Field(5, description="检索文档数量", ge=1, le=20)

class RegulationRequest(BaseModel):
    """法规查询请求模型"""
    regulation_name: str = Field(..., description="法规名称", min_length=1, max_length=100)

class QuestionResponse(BaseModel):
    """问题回答响应模型"""
    question: str
    answer: str
    timestamp: str
    relevant_documents: int
    sources: Optional[List[Dict[str, Any]]] = None
    error: Optional[str] = None

class RiskAssessmentResponse(BaseModel):
    """风险评估响应模型"""
    scenario: str
    assessment: str
    timestamp: str
    knowledge_sources: int
    error: Optional[str] = None

class RegulationResponse(BaseModel):
    """法规查询响应模型"""
    regulation: str
    found: bool
    content: Optional[str] = None
    chunks: Optional[int] = None
    message: Optional[str] = None
    timestamp: str
    error: Optional[str] = None

class SystemStatsResponse(BaseModel):
    """系统统计响应模型"""
    database: Dict[str, Any]
    memory_enabled: bool
    llm_type: str
    timestamp: str
    error: Optional[str] = None

# FastAPI应用
app = FastAPI(
    title="风控AI问答系统API",
    description="基于AI的公益基金会风险控制智能问答系统",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 依赖注入
async def get_qa_system() -> RiskControlQASystem:
    """获取问答系统实例"""
    global qa_system
    if qa_system is None:
        raise HTTPException(status_code=503, detail="问答系统未初始化")
    return qa_system

# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时初始化"""
    global qa_system
    try:
        logger.info("正在初始化风控AI问答系统...")
        
        # 初始化向量数据库
        chroma_manager = ChromaManager()
        
        # 检查数据库状态
        stats = chroma_manager.get_collection_stats()
        if stats.get('total_documents', 0) == 0:
            logger.warning("向量数据库为空，请先运行文档导入脚本")
        
        # 初始化问答系统
        qa_system = RiskControlQASystem(chroma_manager)
        
        logger.info(f"系统初始化完成，数据库包含 {stats.get('total_documents', 0)} 个文档")
        
    except Exception as e:
        logger.error(f"系统初始化失败: {e}")
        qa_system = None

# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理"""
    logger.info("正在关闭风控AI问答系统...")

# API路由
@app.get("/", response_model=Dict[str, str])
async def root():
    """根路径"""
    return {
        "message": "风控AI问答系统API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }

@app.get("/health", response_model=Dict[str, Any])
async def health_check():
    """健康检查"""
    global qa_system
    
    status = "healthy" if qa_system is not None else "unhealthy"
    
    return {
        "status": status,
        "timestamp": datetime.now().isoformat(),
        "system_initialized": qa_system is not None
    }

@app.post("/ask", response_model=QuestionResponse)
async def ask_question(
    request: QuestionRequest,
    qa_system: RiskControlQASystem = Depends(get_qa_system)
):
    """回答用户问题"""
    try:
        logger.info(f"收到问题: {request.question[:50]}...")
        
        result = qa_system.ask_question(
            question=request.question,
            search_k=request.search_k,
            include_sources=request.include_sources
        )
        
        return QuestionResponse(**result)
        
    except Exception as e:
        logger.error(f"处理问题时出错: {e}")
        raise HTTPException(status_code=500, detail=f"处理问题时出错: {str(e)}")

@app.post("/assess-risk", response_model=RiskAssessmentResponse)
async def assess_risk(
    request: RiskAssessmentRequest,
    qa_system: RiskControlQASystem = Depends(get_qa_system)
):
    """风险评估"""
    try:
        logger.info(f"收到风险评估请求: {request.scenario[:50]}...")
        
        result = qa_system.assess_risk(
            scenario=request.scenario,
            search_k=request.search_k
        )
        
        return RiskAssessmentResponse(**result)
        
    except Exception as e:
        logger.error(f"风险评估时出错: {e}")
        raise HTTPException(status_code=500, detail=f"风险评估时出错: {str(e)}")

@app.post("/regulation", response_model=RegulationResponse)
async def get_regulation(
    request: RegulationRequest,
    qa_system: RiskControlQASystem = Depends(get_qa_system)
):
    """获取法规信息"""
    try:
        logger.info(f"查询法规: {request.regulation_name}")
        
        result = qa_system.get_regulation_info(request.regulation_name)
        
        return RegulationResponse(**result)
        
    except Exception as e:
        logger.error(f"查询法规时出错: {e}")
        raise HTTPException(status_code=500, detail=f"查询法规时出错: {str(e)}")

@app.get("/stats", response_model=SystemStatsResponse)
async def get_system_stats(
    qa_system: RiskControlQASystem = Depends(get_qa_system)
):
    """获取系统统计信息"""
    try:
        result = qa_system.get_system_stats()
        return SystemStatsResponse(**result)
        
    except Exception as e:
        logger.error(f"获取系统统计时出错: {e}")
        raise HTTPException(status_code=500, detail=f"获取系统统计时出错: {str(e)}")

@app.post("/clear-memory")
async def clear_memory(
    qa_system: RiskControlQASystem = Depends(get_qa_system)
):
    """清除对话记忆"""
    try:
        qa_system.clear_memory()
        return {"message": "对话记忆已清除", "timestamp": datetime.now().isoformat()}
        
    except Exception as e:
        logger.error(f"清除记忆时出错: {e}")
        raise HTTPException(status_code=500, detail=f"清除记忆时出错: {str(e)}")

@app.get("/search", response_model=Dict[str, Any])
async def search_documents(
    query: str,
    k: int = 5,
    qa_system: RiskControlQASystem = Depends(get_qa_system)
):
    """搜索文档"""
    try:
        if not query.strip():
            raise HTTPException(status_code=400, detail="查询不能为空")
        
        logger.info(f"搜索文档: {query[:50]}...")
        
        results = qa_system.chroma_manager.search_documents(query, n_results=k)
        
        return {
            "query": query,
            "results": results,
            "count": len(results),
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"搜索文档时出错: {e}")
        raise HTTPException(status_code=500, detail=f"搜索文档时出错: {str(e)}")

# 异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    logger.error(f"未处理的异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "内部服务器错误",
            "detail": str(exc),
            "timestamp": datetime.now().isoformat()
        }
    )

def create_app() -> FastAPI:
    """创建FastAPI应用"""
    return app

if __name__ == "__main__":
    # 开发环境运行
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
