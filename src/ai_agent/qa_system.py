#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI智能问答系统核心模块
基于LangChain实现的风控知识问答系统
"""

import os
import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import json
from datetime import datetime

from langchain.llms.base import LLM
from langchain.schema import BaseMessage, HumanMessage, AIMessage, SystemMessage
from langchain.prompts import PromptTemplate, ChatPromptTemplate
from langchain.chains import LLMChain, RetrievalQA
from langchain.memory import ConversationBufferMemory
from langchain.callbacks.manager import CallbackManagerForLLMRun
from langchain.vectorstores.base import VectorStore
from langchain.embeddings.base import Embeddings

import sys
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.vector_db.chroma_manager import ChromaManager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LocalLLM(LLM):
    """本地LLM包装器，用于集成Ollama等本地模型"""

    model_name: str = "qwen2.5:7b"
    base_url: str = "http://localhost:11434"
    temperature: float = 0.7
    max_tokens: int = 2048

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        try:
            import requests
            # 使用object.__setattr__来避免Pydantic的字段验证
            object.__setattr__(self, 'session', requests.Session())
        except ImportError:
            logger.error("需要安装requests库: pip install requests")
            raise
    
    @property
    def _llm_type(self) -> str:
        return "local_llm"
    
    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        """调用本地LLM"""
        try:
            import requests
            
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": self.temperature,
                    "num_predict": self.max_tokens,
                }
            }
            
            if stop:
                payload["stop"] = stop
            
            response = object.__getattribute__(self, 'session').post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("response", "")
            else:
                logger.error(f"LLM调用失败: {response.status_code} - {response.text}")
                return "抱歉，AI服务暂时不可用，请稍后重试。"
                
        except Exception as e:
            logger.error(f"LLM调用异常: {e}")
            return "抱歉，处理您的请求时出现错误，请稍后重试。"

class ChromaVectorStore(VectorStore):
    """Chroma向量存储的LangChain适配器"""
    
    def __init__(self, chroma_manager: ChromaManager):
        self.chroma_manager = chroma_manager
    
    def add_texts(
        self,
        texts: List[str],
        metadatas: Optional[List[dict]] = None,
        **kwargs: Any,
    ) -> List[str]:
        """添加文本到向量存储"""
        # 这里可以实现批量添加功能
        return []
    
    def similarity_search(
        self,
        query: str,
        k: int = 4,
        **kwargs: Any,
    ) -> List[Dict[str, Any]]:
        """相似性搜索"""
        results = self.chroma_manager.search_documents(query, n_results=k)
        
        # 转换为LangChain格式
        documents = []
        for result in results:
            doc = {
                "page_content": result["content"],
                "metadata": result["metadata"]
            }
            documents.append(doc)
        
        return documents
    
    @classmethod
    def from_texts(
        cls,
        texts: List[str],
        embedding: Embeddings,
        metadatas: Optional[List[dict]] = None,
        **kwargs: Any,
    ):
        """从文本创建向量存储"""
        # 这里可以实现从文本创建的功能
        pass

class RiskControlQASystem:
    """风控智能问答系统"""
    
    def __init__(self, 
                 chroma_manager: ChromaManager,
                 llm: Optional[LLM] = None,
                 enable_memory: bool = True):
        """
        初始化问答系统
        
        Args:
            chroma_manager: 向量数据库管理器
            llm: 语言模型，如果为None则使用默认的本地LLM
            enable_memory: 是否启用对话记忆
        """
        self.chroma_manager = chroma_manager
        self.llm = llm or LocalLLM()
        self.vector_store = ChromaVectorStore(chroma_manager)
        
        # 初始化记忆
        self.memory = ConversationBufferMemory(
            memory_key="chat_history",
            return_messages=True
        ) if enable_memory else None
        
        # 初始化提示模板
        self._init_prompts()
        
        # 初始化链
        self._init_chains()
        
        logger.info("风控智能问答系统初始化完成")
    
    def _init_prompts(self):
        """初始化提示模板"""
        
        # 系统提示
        self.system_prompt = """你是一个专业的公益基金会风险控制专家助手。你的任务是基于提供的风控知识库内容，为用户提供准确、专业的风险管理建议。

请遵循以下原则：
1. 基于提供的知识库内容回答问题，确保信息准确性
2. 如果知识库中没有相关信息，请明确说明并建议咨询专业人士
3. 提供具体、可操作的建议
4. 使用专业但易懂的语言
5. 重点关注合规性和风险防范

知识库内容包括：
- 相关法律法规（慈善法、基金会管理条例等）
- 风控管理指南和最佳实践
- 声誉风险管控策略
- 社交媒体监测配置

请根据用户问题，结合相关的知识库内容，提供专业的风控建议。"""

        # 问答提示模板
        self.qa_prompt = PromptTemplate(
            input_variables=["context", "question", "chat_history"],
            template="""基于以下风控知识库内容回答用户问题：

相关知识库内容：
{context}

对话历史：
{chat_history}

用户问题：{question}

请基于上述知识库内容，为用户提供专业的风控建议。如果知识库中没有直接相关的信息，请说明并建议咨询专业人士。

回答："""
        )
        
        # 风险评估提示模板
        self.risk_assessment_prompt = PromptTemplate(
            input_variables=["scenario", "context"],
            template="""作为风控专家，请对以下场景进行风险评估：

场景描述：{scenario}

相关风控知识：
{context}

请从以下维度进行评估：
1. 风险识别：识别潜在的风险点
2. 风险等级：评估风险的严重程度（高/中/低）
3. 影响分析：分析可能的影响和后果
4. 应对建议：提供具体的风险防控措施
5. 合规要求：相关的法规要求和合规建议

风险评估报告："""
        )
    
    def _init_chains(self):
        """初始化LangChain链"""
        
        # 基础问答链
        self.qa_chain = LLMChain(
            llm=self.llm,
            prompt=self.qa_prompt,
            memory=self.memory,
            verbose=True
        )
        
        # 风险评估链
        self.risk_assessment_chain = LLMChain(
            llm=self.llm,
            prompt=self.risk_assessment_prompt,
            verbose=True
        )
    
    def ask_question(self, 
                    question: str,
                    search_k: int = 5,
                    include_sources: bool = True) -> Dict[str, Any]:
        """
        回答用户问题
        
        Args:
            question: 用户问题
            search_k: 检索的文档数量
            include_sources: 是否包含信息来源
            
        Returns:
            包含答案和相关信息的字典
        """
        try:
            # 检索相关文档
            relevant_docs = self.vector_store.similarity_search(question, k=search_k)
            
            # 构建上下文
            context = "\n\n".join([
                f"文档{i+1}：{doc['page_content']}"
                for i, doc in enumerate(relevant_docs)
            ])
            
            # 获取对话历史
            chat_history = ""
            if self.memory:
                history = self.memory.chat_memory.messages
                chat_history = "\n".join([
                    f"{'用户' if isinstance(msg, HumanMessage) else 'AI'}：{msg.content}"
                    for msg in history[-6:]  # 只保留最近3轮对话
                ])
            
            # 生成回答
            response = self.qa_chain.run(
                context=context,
                question=question,
                chat_history=chat_history
            )
            
            # 构建返回结果
            result = {
                "question": question,
                "answer": response,
                "timestamp": datetime.now().isoformat(),
                "relevant_documents": len(relevant_docs)
            }
            
            if include_sources:
                result["sources"] = [
                    {
                        "content": doc["page_content"][:200] + "...",
                        "metadata": doc["metadata"]
                    }
                    for doc in relevant_docs
                ]
            
            logger.info(f"成功回答问题: {question[:50]}...")
            return result
            
        except Exception as e:
            logger.error(f"回答问题时出错: {e}")
            return {
                "question": question,
                "answer": "抱歉，处理您的问题时出现错误，请稍后重试。",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def assess_risk(self, scenario: str, search_k: int = 5) -> Dict[str, Any]:
        """
        风险评估
        
        Args:
            scenario: 风险场景描述
            search_k: 检索的文档数量
            
        Returns:
            风险评估结果
        """
        try:
            # 检索相关风控知识
            relevant_docs = self.vector_store.similarity_search(
                f"风险评估 风险管控 {scenario}", 
                k=search_k
            )
            
            # 构建上下文
            context = "\n\n".join([
                f"相关知识{i+1}：{doc['page_content']}"
                for i, doc in enumerate(relevant_docs)
            ])
            
            # 生成风险评估
            assessment = self.risk_assessment_chain.run(
                scenario=scenario,
                context=context
            )
            
            result = {
                "scenario": scenario,
                "assessment": assessment,
                "timestamp": datetime.now().isoformat(),
                "knowledge_sources": len(relevant_docs)
            }
            
            logger.info(f"完成风险评估: {scenario[:50]}...")
            return result
            
        except Exception as e:
            logger.error(f"风险评估时出错: {e}")
            return {
                "scenario": scenario,
                "assessment": "抱歉，进行风险评估时出现错误，请稍后重试。",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    def get_regulation_info(self, regulation_name: str) -> Dict[str, Any]:
        """
        获取法规信息
        
        Args:
            regulation_name: 法规名称
            
        Returns:
            法规信息
        """
        try:
            # 搜索特定法规
            results = self.chroma_manager.search_documents(
                regulation_name,
                n_results=10,
                filter_metadata={"type": "regulation"}
            )
            
            if not results:
                return {
                    "regulation": regulation_name,
                    "found": False,
                    "message": f"未找到关于'{regulation_name}'的法规信息"
                }
            
            # 整理法规内容
            content_parts = []
            for result in results:
                content_parts.append(result["content"])
            
            return {
                "regulation": regulation_name,
                "found": True,
                "content": "\n\n".join(content_parts),
                "chunks": len(results),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取法规信息时出错: {e}")
            return {
                "regulation": regulation_name,
                "found": False,
                "error": str(e),
                "message": "获取法规信息时出现错误"
            }
    
    def clear_memory(self):
        """清除对话记忆"""
        if self.memory:
            self.memory.clear()
            logger.info("对话记忆已清除")
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        try:
            db_stats = self.chroma_manager.get_collection_stats()
            
            return {
                "database": db_stats,
                "memory_enabled": self.memory is not None,
                "llm_type": self.llm._llm_type,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取系统统计信息时出错: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }


def main():
    """测试函数"""
    # 初始化向量数据库
    chroma_manager = ChromaManager()
    
    # 初始化问答系统
    qa_system = RiskControlQASystem(chroma_manager)
    
    # 测试问题
    test_questions = [
        "基金会在财务管理方面需要注意哪些风险？",
        "如何建立有效的声誉风险管控机制？",
        "慈善法对基金会信息披露有什么要求？"
    ]
    
    print("=== 风控智能问答系统测试 ===\n")
    
    for question in test_questions:
        print(f"问题：{question}")
        result = qa_system.ask_question(question)
        print(f"回答：{result['answer']}")
        print(f"相关文档数：{result['relevant_documents']}")
        print("-" * 80)
    
    # 测试风险评估
    print("\n=== 风险评估测试 ===\n")
    scenario = "基金会计划与某企业合作开展大型公益项目，涉及资金1000万元"
    assessment = qa_system.assess_risk(scenario)
    print(f"场景：{scenario}")
    print(f"评估：{assessment['assessment']}")
    
    # 获取系统统计
    stats = qa_system.get_system_stats()
    print(f"\n系统统计：{json.dumps(stats, ensure_ascii=False, indent=2)}")


if __name__ == "__main__":
    main()
