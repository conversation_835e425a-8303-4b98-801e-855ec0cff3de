#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向量数据库管理模块
使用Chroma作为向量数据库，支持文档存储、检索和相似性搜索
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import chromadb
from chromadb.config import Settings
from chromadb.utils import embedding_functions
import jieba
import zhconv
from sentence_transformers import SentenceTransformer

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ChromaManager:
    """Chroma向量数据库管理器"""
    
    def __init__(self, 
                 persist_directory: str = "data/vector_db",
                 collection_name: str = "risk_control_knowledge",
                 embedding_model: str = "all-MiniLM-L6-v2"):
        """
        初始化向量数据库管理器
        
        Args:
            persist_directory: 数据库持久化目录
            collection_name: 集合名称
            embedding_model: 嵌入模型名称
        """
        self.persist_directory = persist_directory
        self.collection_name = collection_name
        self.embedding_model_name = embedding_model
        
        # 确保目录存在
        Path(persist_directory).mkdir(parents=True, exist_ok=True)
        
        # 初始化Chroma客户端
        self.client = chromadb.PersistentClient(
            path=persist_directory,
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # 初始化嵌入模型
        self.embedding_model = SentenceTransformer(embedding_model)
        
        # 创建或获取集合
        self.collection = self._get_or_create_collection()
        
        logger.info(f"ChromaManager初始化完成，数据库路径: {persist_directory}")
    
    def _get_or_create_collection(self):
        """获取或创建集合"""
        try:
            # 尝试获取现有集合
            collection = self.client.get_collection(
                name=self.collection_name,
                embedding_function=embedding_functions.SentenceTransformerEmbeddingFunction(
                    model_name=self.embedding_model_name
                )
            )
            logger.info(f"获取现有集合: {self.collection_name}")
        except Exception:
            # 创建新集合
            collection = self.client.create_collection(
                name=self.collection_name,
                embedding_function=embedding_functions.SentenceTransformerEmbeddingFunction(
                    model_name=self.embedding_model_name
                ),
                metadata={"description": "风控知识库向量数据库"}
            )
            logger.info(f"创建新集合: {self.collection_name}")
        
        return collection
    
    def preprocess_text(self, text: str) -> str:
        """
        文本预处理
        
        Args:
            text: 原始文本
            
        Returns:
            处理后的文本
        """
        # 转换为简体中文
        text = zhconv.convert(text, 'zh-cn')
        
        # 去除多余空白字符
        text = ' '.join(text.split())
        
        return text
    
    def split_text_into_chunks(self, text: str, chunk_size: int = 500, overlap: int = 50) -> List[str]:
        """
        将长文本分割为块
        
        Args:
            text: 原始文本
            chunk_size: 块大小（字符数）
            overlap: 重叠字符数
            
        Returns:
            文本块列表
        """
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            chunk = text[start:end]
            
            # 如果不是最后一块，尝试在句号处分割
            if end < len(text):
                last_period = chunk.rfind('。')
                if last_period > chunk_size * 0.7:  # 如果句号位置合理
                    chunk = chunk[:last_period + 1]
                    end = start + last_period + 1
            
            chunks.append(chunk.strip())
            start = end - overlap
            
            if start >= len(text):
                break
        
        return [chunk for chunk in chunks if len(chunk.strip()) > 10]
    
    def add_document(self, 
                    document_id: str,
                    content: str,
                    metadata: Dict[str, Any],
                    chunk_size: int = 500) -> bool:
        """
        添加文档到向量数据库
        
        Args:
            document_id: 文档ID
            content: 文档内容
            metadata: 文档元数据
            chunk_size: 文本块大小
            
        Returns:
            是否成功添加
        """
        try:
            # 预处理文本
            processed_content = self.preprocess_text(content)
            
            # 分割文本
            chunks = self.split_text_into_chunks(processed_content, chunk_size)
            
            # 准备数据
            ids = []
            documents = []
            metadatas = []
            
            for i, chunk in enumerate(chunks):
                chunk_id = f"{document_id}_chunk_{i}"
                chunk_metadata = {
                    **metadata,
                    "chunk_index": i,
                    "total_chunks": len(chunks),
                    "document_id": document_id
                }
                
                ids.append(chunk_id)
                documents.append(chunk)
                metadatas.append(chunk_metadata)
            
            # 添加到集合
            self.collection.add(
                ids=ids,
                documents=documents,
                metadatas=metadatas
            )
            
            logger.info(f"成功添加文档 {document_id}，共 {len(chunks)} 个文本块")
            return True
            
        except Exception as e:
            logger.error(f"添加文档失败: {e}")
            return False
    
    def search_documents(self, 
                        query: str,
                        n_results: int = 10,
                        filter_metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        搜索相关文档
        
        Args:
            query: 查询文本
            n_results: 返回结果数量
            filter_metadata: 元数据过滤条件
            
        Returns:
            搜索结果列表
        """
        try:
            # 预处理查询文本
            processed_query = self.preprocess_text(query)
            
            # 执行搜索
            results = self.collection.query(
                query_texts=[processed_query],
                n_results=n_results,
                where=filter_metadata
            )
            
            # 格式化结果
            formatted_results = []
            for i in range(len(results['ids'][0])):
                result = {
                    'id': results['ids'][0][i],
                    'content': results['documents'][0][i],
                    'metadata': results['metadatas'][0][i],
                    'distance': results['distances'][0][i] if 'distances' in results else None
                }
                formatted_results.append(result)
            
            logger.info(f"搜索查询 '{query}' 返回 {len(formatted_results)} 个结果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return []
    
    def get_document_by_id(self, document_id: str) -> List[Dict[str, Any]]:
        """
        根据文档ID获取文档所有块
        
        Args:
            document_id: 文档ID
            
        Returns:
            文档块列表
        """
        try:
            results = self.collection.get(
                where={"document_id": document_id}
            )
            
            formatted_results = []
            for i in range(len(results['ids'])):
                result = {
                    'id': results['ids'][i],
                    'content': results['documents'][i],
                    'metadata': results['metadatas'][i]
                }
                formatted_results.append(result)
            
            # 按chunk_index排序
            formatted_results.sort(key=lambda x: x['metadata'].get('chunk_index', 0))
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"获取文档失败: {e}")
            return []
    
    def delete_document(self, document_id: str) -> bool:
        """
        删除文档
        
        Args:
            document_id: 文档ID
            
        Returns:
            是否成功删除
        """
        try:
            # 获取所有相关的chunk ID
            results = self.collection.get(
                where={"document_id": document_id}
            )
            
            if results['ids']:
                self.collection.delete(ids=results['ids'])
                logger.info(f"成功删除文档 {document_id}，共 {len(results['ids'])} 个文本块")
                return True
            else:
                logger.warning(f"未找到文档 {document_id}")
                return False
                
        except Exception as e:
            logger.error(f"删除文档失败: {e}")
            return False
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """
        获取集合统计信息
        
        Returns:
            统计信息字典
        """
        try:
            count = self.collection.count()
            
            # 获取所有文档的元数据
            all_data = self.collection.get()
            
            # 统计文档类型
            doc_types = {}
            documents = set()
            
            for metadata in all_data['metadatas']:
                doc_type = metadata.get('type', 'unknown')
                doc_types[doc_type] = doc_types.get(doc_type, 0) + 1
                documents.add(metadata.get('document_id', ''))
            
            stats = {
                'total_chunks': count,
                'total_documents': len(documents),
                'document_types': doc_types,
                'collection_name': self.collection_name
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def reset_collection(self) -> bool:
        """
        重置集合（删除所有数据）
        
        Returns:
            是否成功重置
        """
        try:
            self.client.delete_collection(self.collection_name)
            self.collection = self._get_or_create_collection()
            logger.info(f"成功重置集合 {self.collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"重置集合失败: {e}")
            return False


def main():
    """测试函数"""
    # 初始化向量数据库管理器
    chroma_manager = ChromaManager()
    
    # 获取统计信息
    stats = chroma_manager.get_collection_stats()
    print(f"集合统计信息: {json.dumps(stats, ensure_ascii=False, indent=2)}")
    
    # 测试搜索
    results = chroma_manager.search_documents("基金会管理", n_results=5)
    print(f"搜索结果数量: {len(results)}")
    for result in results[:3]:
        print(f"ID: {result['id']}")
        print(f"内容: {result['content'][:100]}...")
        print(f"元数据: {result['metadata']}")
        print("-" * 50)


if __name__ == "__main__":
    main()
