# 风控AI问答系统用户指南

## 系统概述

风控AI问答系统是专为中国公益基金会设计的智能风险控制问答平台，基于先进的AI技术和完整的法规知识库，为基金会提供专业的风控建议和合规指导。

### 核心功能

- **智能问答**：基于法规知识库的专业风控问答
- **风险评估**：针对具体场景的风险分析和建议
- **法规查询**：快速检索相关法律法规条文
- **文档搜索**：全文检索风控知识库内容
- **声誉监测**：社交媒体风险监测和预警

### 技术特色

- **本地化部署**：支持完全本地化部署，保护数据安全
- **开源框架**：基于LangChain、Chroma等开源技术
- **容器化**：支持Docker/Podman容器化部署
- **中文优化**：针对中文环境优化的文本处理
- **多模态接口**：提供API和Web界面两种访问方式

## 快速开始

### 环境要求

- **操作系统**：Linux (推荐Arch Linux)
- **Python版本**：3.11+
- **内存要求**：至少8GB RAM
- **存储空间**：至少10GB可用空间
- **GPU**：可选，用于加速AI推理

### 安装部署

#### 方式一：直接安装

1. **克隆项目**
```bash
git clone <项目地址>
cd 风控AI问答系统
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **初始化环境**
```bash
./scripts/start_services.sh init
```

4. **导入文档**
```bash
python scripts/import_documents.py --reset --test
```

5. **启动服务**
```bash
./scripts/start_services.sh start
```

#### 方式二：Docker部署

1. **构建镜像**
```bash
docker-compose build
```

2. **启动服务**
```bash
docker-compose up -d
```

3. **检查状态**
```bash
docker-compose ps
```

### 访问系统

- **Web界面**：http://localhost:8501
- **API文档**：http://localhost:8000/docs
- **健康检查**：http://localhost:8000/health

## 功能使用

### 智能问答

智能问答是系统的核心功能，可以回答各种风控相关问题。

#### 使用方法

1. **Web界面**
   - 访问 http://localhost:8501
   - 选择"智能问答"功能
   - 输入问题并提交

2. **API调用**
```bash
curl -X POST "http://localhost:8000/ask" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "基金会在财务管理方面需要注意哪些风险？",
    "search_k": 5,
    "include_sources": true
  }'
```

#### 常见问题示例

- 基金会在财务管理方面需要注意哪些风险？
- 如何建立有效的声誉风险管控机制？
- 慈善法对基金会信息披露有什么要求？
- 基金会如何防范网络安全风险？
- 公益项目执行中的主要风险点有哪些？

### 风险评估

针对具体场景进行专业的风险分析。

#### 使用方法

1. **Web界面**
   - 选择"风险评估"功能
   - 描述风险场景
   - 获取评估报告

2. **API调用**
```bash
curl -X POST "http://localhost:8000/assess-risk" \
  -H "Content-Type: application/json" \
  -d '{
    "scenario": "基金会计划与某企业合作开展大型公益项目，涉及资金1000万元",
    "search_k": 5
  }'
```

#### 评估维度

- **风险识别**：识别潜在风险点
- **风险等级**：评估严重程度
- **影响分析**：分析可能后果
- **应对建议**：提供防控措施
- **合规要求**：相关法规要求

### 法规查询

快速检索相关法律法规条文。

#### 支持的法规

- 慈善法
- 基金会管理条例
- 公益事业捐赠法
- 个人信息保护法
- 网络安全法
- 数据安全法
- 民间非营利组织会计制度

#### 使用方法

```bash
curl -X POST "http://localhost:8000/regulation" \
  -H "Content-Type: application/json" \
  -d '{
    "regulation_name": "慈善法"
  }'
```

### 文档搜索

全文检索风控知识库内容。

#### 使用方法

```bash
curl "http://localhost:8000/search?query=财务管理&k=5"
```

## 系统管理

### 服务管理

使用启动脚本管理系统服务：

```bash
# 初始化环境
./scripts/start_services.sh init

# 启动所有服务
./scripts/start_services.sh start

# 停止所有服务
./scripts/start_services.sh stop

# 重启服务
./scripts/start_services.sh restart

# 查看状态
./scripts/start_services.sh status

# 仅启动API服务
./scripts/start_services.sh api

# 仅启动Web界面
./scripts/start_services.sh web
```

### 数据管理

#### 文档导入

```bash
# 重置并导入所有文档
python scripts/import_documents.py --reset --test

# 仅导入新文档
python scripts/import_documents.py

# 导入特定类型文档
python scripts/import_documents.py --regulations-only
```

#### 数据备份

```bash
# 备份向量数据库
cp -r data/vector_db data/vector_db_backup_$(date +%Y%m%d)

# 备份日志
cp -r logs logs_backup_$(date +%Y%m%d)
```

### 监控运维

#### 健康检查

```bash
# API健康检查
curl http://localhost:8000/health

# 系统统计
curl http://localhost:8000/stats
```

#### 日志查看

```bash
# API服务日志
tail -f logs/api.log

# Web服务日志
tail -f logs/web.log

# 系统日志
journalctl -u risk-control-ai -f
```

## 配置说明

### 环境变量

- `PYTHONPATH`：Python路径设置
- `API_HOST`：API服务主机地址
- `API_PORT`：API服务端口
- `WEB_HOST`：Web服务主机地址
- `WEB_PORT`：Web服务端口
- `OLLAMA_HOST`：Ollama服务地址

### 配置文件

- `config/app.yaml`：应用配置
- `config/prometheus.yml`：监控配置
- `data/knowledge/社交媒体监测配置.json`：监测配置

## 故障排除

### 常见问题

#### 1. 服务启动失败

**问题**：API服务无法启动
**解决**：
```bash
# 检查端口占用
lsof -i :8000

# 检查依赖
pip install -r requirements.txt

# 查看错误日志
tail -f logs/api.log
```

#### 2. 向量数据库为空

**问题**：搜索无结果
**解决**：
```bash
# 重新导入文档
python scripts/import_documents.py --reset
```

#### 3. Ollama连接失败

**问题**：AI回答异常
**解决**：
```bash
# 启动Ollama服务
ollama serve

# 安装模型
ollama pull qwen2.5:7b
```

#### 4. 内存不足

**问题**：系统运行缓慢
**解决**：
- 增加系统内存
- 调整模型参数
- 使用更小的嵌入模型

### 性能优化

#### 1. 硬件优化

- **CPU**：多核处理器，推荐8核以上
- **内存**：16GB以上RAM
- **存储**：SSD硬盘
- **GPU**：NVIDIA GPU（可选）

#### 2. 软件优化

```bash
# 使用GPU加速
export CUDA_VISIBLE_DEVICES=0

# 调整工作进程数
uvicorn src.api.main:app --workers 4

# 启用缓存
redis-server
```

## 安全建议

### 数据安全

1. **本地部署**：避免敏感数据上传到云端
2. **访问控制**：设置防火墙规则
3. **数据加密**：对敏感数据进行加密存储
4. **定期备份**：建立数据备份机制

### 网络安全

1. **HTTPS**：生产环境使用HTTPS
2. **认证授权**：实施用户认证和权限控制
3. **API限流**：防止API滥用
4. **日志审计**：记录所有操作日志

## 技术支持

### 联系方式

- **技术文档**：查看项目README和docs目录
- **问题反馈**：提交GitHub Issue
- **社区讨论**：参与项目讨论

### 开发指南

如需二次开发，请参考：
- `docs/开发指南.md`
- `docs/API文档.md`
- `docs/架构设计.md`

## 更新日志

### v1.0.0 (2025-01-25)

- ✅ 完成基础架构设计和项目初始化
- ✅ 实现法规文档自动收集和整理
- ✅ 完成中国互联网环境声誉风险分析
- ✅ 构建Chroma向量数据库，包含80个文档块
- ✅ 开发AI问答系统核心功能
- ✅ 提供FastAPI和Streamlit双界面
- ✅ 支持Docker/Podman容器化部署
- ✅ 完成系统测试和文档编写

### 系统特色

- **知识库完整**：包含9大核心法规和专业风控指南
- **AI技术先进**：基于LangChain和向量检索技术
- **中文优化**：针对中文环境的文本处理和分析
- **部署灵活**：支持本地部署和容器化部署
- **安全可控**：完全本地化，保护数据安全

### 后续规划

- 🔄 集成Ollama本地大语言模型
- 🔄 增强声誉风险实时监测能力
- 🔄 扩展更多法规和政策支持
- 🔄 优化AI回答质量和准确性
- 🔄 增加用户权限管理功能
