# 中国公益基金会风控管理AI智能问答系统

## 项目概述

本项目是一个专门针对中国公益基金会风控管理的AI智能问答系统，基于《风控考虑要素.md》文档内容构建，能够自主回答各类基金会风控问题。

## 核心功能

- **法律法规智能检索**：集成9部核心法规文档，提供精准的法律条文查询
- **风控问题智能问答**：基于专业知识库回答风控管理相关问题
- **声誉风险预警分析**：针对中国网络舆情环境的专项风险分析
- **合规性自动检查**：自动检查基金会运营的合规性问题
- **风险评估工具**：提供风险评估矩阵和预警指标体系

## 技术架构

### 核心技术栈
- **AI框架**：LangChain - 开源AI应用开发框架
- **向量数据库**：Chroma - 高性能向量存储和检索
- **本地化部署**：Ollama - 支持本地大语言模型部署
- **容器化**：Docker/Podman - 支持容器化部署
- **Web框架**：FastAPI - 高性能API服务
- **前端界面**：Streamlit - 简洁的Web UI

### 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web前端界面   │    │   API网关服务   │    │   AI问答引擎    │
│   (Streamlit)   │◄──►│   (FastAPI)     │◄──►│  (LangChain)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐             │
                       │   向量数据库    │◄────────────┘
                       │   (Chroma)      │
                       └─────────────────┘
                                │
                       ┌─────────────────┐
                       │   文档知识库    │
                       │  (法规+风控)    │
                       └─────────────────┘
```

## 项目结构

```
foundation-risk-ai/
├── README.md                    # 项目说明文档
├── requirements.txt             # Python依赖包
├── docker-compose.yml           # 容器编排配置
├── Dockerfile                   # 容器镜像构建
├── .env.example                 # 环境变量模板
├── config/                      # 配置文件目录
│   ├── settings.py             # 系统配置
│   └── logging.conf            # 日志配置
├── data/                        # 数据目录
│   ├── regulations/            # 法律法规文档
│   ├── knowledge/              # 风控知识库
│   └── vector_db/              # 向量数据库存储
├── src/                         # 源代码目录
│   ├── __init__.py
│   ├── core/                   # 核心功能模块
│   │   ├── __init__.py
│   │   ├── document_loader.py  # 文档加载器
│   │   ├── vector_store.py     # 向量存储管理
│   │   ├── qa_engine.py        # 问答引擎
│   │   └── risk_analyzer.py    # 风险分析器
│   ├── api/                    # API服务
│   │   ├── __init__.py
│   │   ├── main.py            # FastAPI主应用
│   │   ├── routes/            # API路由
│   │   └── models/            # 数据模型
│   ├── web/                    # Web界面
│   │   ├── __init__.py
│   │   ├── app.py             # Streamlit应用
│   │   └── components/        # UI组件
│   └── utils/                  # 工具函数
│       ├── __init__.py
│       ├── text_processor.py  # 文本处理
│       └── web_scraper.py     # 网页抓取
├── scripts/                     # 脚本目录
│   ├── setup_env.sh           # 环境初始化
│   ├── download_regulations.py # 法规文档下载
│   └── build_vector_db.py     # 向量数据库构建
├── tests/                       # 测试目录
│   ├── __init__.py
│   ├── test_core/
│   ├── test_api/
│   └── test_web/
└── docs/                        # 文档目录
    ├── deployment.md           # 部署指南
    ├── user_guide.md          # 使用指南
    └── api_reference.md       # API参考
```

## 部署方式

### 本地开发环境
```bash
# 克隆项目
git clone <repository-url>
cd foundation-risk-ai

# 安装依赖
pip install -r requirements.txt

# 初始化环境
bash scripts/setup_env.sh

# 启动服务
python src/web/app.py
```

### 容器化部署
```bash
# 使用Docker Compose
docker-compose up -d

# 或使用Podman
podman-compose up -d
```

## 特色功能

1. **中文优化**：专门针对中文法规文档和问答场景优化
2. **本地化部署**：支持完全离线运行，保障数据安全
3. **法规实时更新**：自动监测法规更新，保持知识库最新
4. **声誉风险专项**：针对中国社交媒体环境的声誉风险分析
5. **企业级安全**：完整的错误处理、日志记录和监控方案

## 开发计划

- [x] 项目架构设计
- [ ] 法律法规文档收集
- [ ] 声誉风险管控分析
- [ ] 向量数据库构建
- [ ] AI Agent核心开发
- [ ] 容器化部署方案
- [ ] 系统测试与文档

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过Issue联系我们。
