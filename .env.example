# 基金会风控AI问答系统环境配置

# 基础配置
APP_NAME=基金会风控AI问答系统
APP_VERSION=1.0.0
DEBUG=false

# API服务配置
API_HOST=0.0.0.0
API_PORT=8000
API_PREFIX=/api/v1

# Web界面配置
WEB_HOST=0.0.0.0
WEB_PORT=8501

# AI模型配置
LLM_MODEL=qwen:7b
LLM_TEMPERATURE=0.1
LLM_MAX_TOKENS=2048

# 向量数据库配置
VECTOR_DB_NAME=foundation_risk_db
EMBEDDING_MODEL=sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# 检索配置
RETRIEVAL_K=5
SIMILARITY_THRESHOLD=0.7

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
LOG_ROTATION=1 day
LOG_RETENTION=30 days

# 安全配置
SECRET_KEY=your-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 缓存配置
CACHE_TTL=3600

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# Ollama配置（本地化AI部署）
OLLAMA_HOST=http://localhost:11434
OLLAMA_MODEL=qwen:7b

# 数据库配置
DATABASE_URL=sqlite:///./data/foundation_risk.db

# 外部API配置（可选）
# OPENAI_API_KEY=your-openai-api-key
# OPENAI_BASE_URL=https://api.openai.com/v1
