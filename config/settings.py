"""
系统配置文件
配置AI问答系统的各项参数
"""

import os
from pathlib import Path
from pydantic_settings import BaseSettings
from typing import List, Optional

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent

class Settings(BaseSettings):
    """系统配置类"""
    
    # 基础配置
    app_name: str = "基金会风控AI问答系统"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # API配置
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    api_prefix: str = "/api/v1"
    
    # Web UI配置
    web_host: str = "0.0.0.0"
    web_port: int = 8501
    
    # 数据目录配置
    data_dir: Path = PROJECT_ROOT / "data"
    regulations_dir: Path = data_dir / "regulations"
    knowledge_dir: Path = data_dir / "knowledge"
    vector_db_dir: Path = data_dir / "vector_db"
    
    # 向量数据库配置
    vector_db_name: str = "foundation_risk_db"
    embedding_model: str = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
    chunk_size: int = 1000
    chunk_overlap: int = 200
    
    # AI模型配置
    llm_model: str = "qwen:7b"  # Ollama模型
    llm_temperature: float = 0.1
    llm_max_tokens: int = 2048
    
    # 检索配置
    retrieval_k: int = 5  # 检索文档数量
    similarity_threshold: float = 0.7
    
    # 法律法规URL配置
    regulation_urls: dict = {
        "慈善法": "http://www.npc.gov.cn/npc/c30834/201603/1b8e2c0e4c5c4d8f8b5c5c5c5c5c5c5c.shtml",
        "基金会管理条例": "http://www.gov.cn/zwgk/2004-03/08/content_2602.htm",
        "基金会信息公布办法": "http://www.mca.gov.cn/article/zwgk/fvfg/shzzgl/200711/20071100003793.shtml",
        "公益事业捐赠法": "http://www.npc.gov.cn/npc/xinwen/2016-09/01/content_1996833.htm",
        "民间非营利组织会计制度": "http://kjs.mof.gov.cn/zhengwuxinxi/zhengcefabu/200805/t20080519_27963.html",
        "基金会财务管理办法": "http://www.mca.gov.cn/article/zwgk/fvfg/shzzgl/200711/20071100003794.shtml",
        "网络安全法": "http://www.npc.gov.cn/npc/xinwen/2016-11/07/content_2001605.htm",
        "数据安全法": "http://www.npc.gov.cn/npc/c30834/202106/7c9af12f51334a73b56d7938f99a788a.shtml",
        "个人信息保护法": "http://www.npc.gov.cn/npc/c30834/202108/a8c4e3672c74491a80b53a172bb753fe.shtml"
    }
    
    # 声誉风险监测配置
    reputation_keywords: List[str] = [
        "基金会", "公益", "慈善", "捐款", "透明度", "公信力",
        "违规", "腐败", "挪用", "欺诈", "丑闻", "质疑"
    ]
    
    # 社交媒体平台配置
    social_platforms: List[str] = ["微博", "微信", "抖音", "知乎", "今日头条"]
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/app.log"
    log_rotation: str = "1 day"
    log_retention: str = "30 days"
    
    # 安全配置
    secret_key: str = "your-secret-key-here"
    access_token_expire_minutes: int = 30
    
    # 缓存配置
    cache_ttl: int = 3600  # 1小时
    
    # 监控配置
    enable_metrics: bool = True
    metrics_port: int = 9090
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

# 创建全局配置实例
settings = Settings()

# 确保数据目录存在
def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        settings.data_dir,
        settings.regulations_dir,
        settings.knowledge_dir,
        settings.vector_db_dir,
        PROJECT_ROOT / "logs"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

# 初始化时创建目录
ensure_directories()
